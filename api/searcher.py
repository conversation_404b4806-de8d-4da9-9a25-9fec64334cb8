"""
CodeMind API - 代码库搜索模块

此模块提供代码库语义搜索功能，返回与查询最相关的代码片段。
"""

import os
import time
import hashlib
import asyncio
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from utils.ticket_utils import prep_snippets
from utils.github_utils import MockClonedRepo
from utils.function_call_graph import get_function_call_chain, FunctionCallGraphBuilder
from config.server import CACHE_DIRECTORY, LLM_API_KEY, LLM_API_BASE, LLM_MODEL
from llm.llm_chat import chat_with_llm

from .common import logger
from .indexer import index_codebase

def codebase_search(repo_directory: str, query: str, limit: int = 5, use_rerank: bool = False, min_score: float = 0.5, complete_imports: bool = False, call_graph: bool = False, use_llm_filter: bool = True) -> Dict[str, Any]:
    """
    搜索代码库

    使用索引搜索代码库，返回最相关的代码片段。
    支持多词查询：按空格分割查询词条，每个词条独立搜索并应用limit和min_score参数。

    参数:
        repo_directory: 代码库目录路径
        query: 搜索查询，支持多词查询（按空格分割）
        limit: 每个词条返回结果数量限制，默认为5
        use_rerank: 是否使用rerank步骤，默认为False（不启用rerank）
        min_score: 最低相关度阈值，默认为0.5，只返回相关度大于等于此值的结果
        complete_imports: 是否补全导入语句，默认为False（不补全导入语句）
        call_graph: 是否显示函数调用链，默认为False（不显示）
        use_llm_filter: 是否使用LLM过滤不相关结果，默认为True（启用过滤）

    返回:
        相关代码片段列表，JSON格式包含code、msg、cost_time和data字段
        多词查询时，总结果数量最多为 词条数量 × limit
    """

    start_time = time.time()
    try:
        # 创建MockClonedRepo对象
        cloned_repo = MockClonedRepo(
            _repo_dir=repo_directory,
            repo_full_name=os.path.basename(repo_directory)
        )

        # 按空格分割查询为多个词条
        query_terms = [term.strip() for term in query.split() if term.strip()]

        if not query_terms:
            return {
                "code": 0,
                "msg": "success",
                "cost_time": 0,
                "data": []
            }

        logger.info(f"多词查询：将查询 '{query}' 分割为 {len(query_terms)} 个词条: {query_terms}")

        all_results = []

        for term in query_terms:
            logger.info(f"搜索词条: '{term}'")

            # 为每个词条独立调用prep_snippets函数
            snippets = prep_snippets(
                cloned_repo=cloned_repo,
                query=term,
                use_multi_query=False,
                NUM_SNIPPETS_TO_KEEP=0,
                skip_analyze_agent=True,
                skip_reranking=not use_rerank,
                k=limit
            )

            term_results = []
            for snippet in snippets:
                passage = ""
                if complete_imports:
                    if snippet.metadata and "imports" in snippet.metadata and snippet.metadata["imports"]:
                        passage = "\n".join(snippet.metadata["imports"])
                passage += snippet.get_snippet(add_ellipsis=False, add_lines=False)

                score = float(snippet.score)
                # 只保留相关度大于等于min_score的结果
                if score >= min_score:
                    # 获取文件路径并转换为相对路径
                    file_path = snippet.original_path if snippet.original_path else snippet.file_path
                    relative_file_path = _convert_to_relative_path(file_path, repo_directory)

                    result_item = {
                        "query": term,  # 记录具体的搜索词条
                        "original_query": query,  # 记录原始完整查询
                        "passage": passage,
                        "file_path": relative_file_path,
                        "start": snippet.start,
                        "end": snippet.end,
                        "score": score
                    }

                    if call_graph:
                        # 检查是否是函数，如果是则添加调用链
                        call_chains = _get_function_call_chains(repo_directory, term, snippet)
                        if call_chains:
                            # 将调用链中的路径转换为相对路径
                            relative_call_chains = _convert_call_chains_to_relative(call_chains, repo_directory)
                            result_item["call_chains"] = relative_call_chains
                            logger.info(f"为函数 {term} 找到 {len(call_chains)} 条调用链")

                    term_results.append(result_item)

            term_results = sorted(term_results, key=lambda x: x["score"], reverse=True)[:limit]
            all_results.extend(term_results)

            logger.info(f"词条 '{term}' 找到 {len(term_results)} 个结果")

        if use_llm_filter and all_results:
            logger.info(f"开始使用LLM过滤 {len(all_results)} 个结果")
            filtered_results = _filter_results_with_llm(all_results)
            logger.info(f"LLM过滤后保留 {len(filtered_results)} 个结果")
            all_results = filtered_results

        # 计算耗时，确保非负值
        elapsed_time = max(0, time.time() - start_time)
        
        # 构建最终响应
        response = {
            "code": 0,
            "msg": "success",
            "cost_time": elapsed_time,
            "data": all_results
        }

        logger.info(f"多词搜索完成，耗时 {elapsed_time:.2f} 秒，总共找到 {len(all_results)} 个结果")
        return response
    except Exception as e:
        logger.exception(f"搜索失败: {str(e)}")
        # 计算耗时，确保非负值（如果start_time已定义）
        elapsed_time = max(0, time.time() - start_time) if 'start_time' in locals() else 0
        error_response = {
            "code": 1,
            "msg": f"搜索失败: {str(e)}",
            "cost_time": elapsed_time,
            "data": []
        }
        return error_response


def _filter_results_with_llm(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    使用LLM并发过滤搜索结果，移除不相关的结果
    
    参数:
        results: 搜索结果列表
    
    返回:
        过滤后的结果列表
    """
    try:
        filtered_results = []
        
        with ThreadPoolExecutor(max_workers=min(10, len(results))) as executor:
            future_to_result = {
                executor.submit(_is_result_relevant, result): result 
                for result in results
            }
            
            for future in as_completed(future_to_result):
                result = future_to_result[future]
                try:
                    is_relevant = future.result()
                    if is_relevant:
                        filtered_results.append(result)
                except Exception as e:
                    logger.warning(f"过滤结果时出错: {e}，保留该结果")
                    filtered_results.append(result)
        
        original_order = {id(r): i for i, r in enumerate(results)}
        filtered_results.sort(key=lambda x: original_order.get(id(x), 999))
        
        return filtered_results
        
    except Exception as e:
        logger.error(f"LLM过滤过程出错: {e}")
        return results


def _is_result_relevant(result: Dict[str, Any]) -> bool:
    """
    使用LLM判断单个搜索结果是否与查询相关
    
    参数:
        result: 单个搜索结果
    
    返回:
        bool: 是否相关
    """
    try:
        original_query = result.get("original_query", "")
        query_str = result.get("query", "")
        passage = result.get("passage", "")
        file_path = result.get("file_path", "")
        
        prompt = f"""评估以下代码片段是否与搜索意图相关。

原始查询: {original_query}
关键词搜索: {query_str}
文件路径: {file_path}

代码片段:
{passage}

评估指南:
1. 用户意图分析: 基于原始查询"{original_query}"理解用户真实搜索意图
2. 代码功能匹配: 代码片段的功能是否满足用户意图或实现了查询描述的功能
3. 关键词相关性: 代码是否包含"{query_str}"中的关键词或其同义词/相关概念
4. 文件路径关联: 文件名或路径是否与查询语义相关
5. 代码内容评估: 是否包含与查询相关的函数、类、变量、注释或逻辑
6. 功能等价性: 不要仅看API名称完全匹配，而应该理解代码功能本质：
   - 相同功能可能有不同实现方式（如各种框架的本地存储、网络请求、状态管理等）
   - 不同命名但实现相同功能的API应视为相关
   - 自定义封装的工具函数与标准库功能相同时应视为相关
   - 跨语言跨框架的同类功能应视为相关

仅回答"是"或"否"表示是否相关。

回答:"""

        response = _call_llm_for_filtering(prompt)
        
        if response:
            response_lower = response.strip().lower()
            if any(word in response_lower for word in ["是", "yes", "相关", "relevant", "true"]):
                return True
            elif any(word in response_lower for word in ["否", "no", "不相关", "不", "irrelevant", "false"]):
                return False
        
        logger.warning(f"无法解析LLM过滤响应: {response}")
        return True
        
    except Exception as e:
        logger.error(f"判断结果相关性时出错: {e}")
        return True

def _call_llm_for_filtering(prompt: str) -> str:
    """
    调用LLM进行过滤判断
    
    参数:
        prompt: 过滤提示词
    
    返回:
        str: LLM响应
    """
    try:
        system_prompt = "你是一个专业的代码搜索引擎评估专家，负责判断代码片段是否与用户查询意图相关。请根据文件路径、代码内容和查询词的语义关联性进行精确评估。重要的是理解代码的功能本质而非表面形式，能够识别不同框架、不同语言中功能等价的实现方式。判断相关性时应注重功能等价性，而非仅仅关键词匹配。只回答\"是\"或\"否\"，不要解释原因。"
        
        response = chat_with_llm(system_prompt, prompt)
        
        if response:
            return response.strip()
        
        return ""
        
    except Exception as e:
        logger.error(f"调用LLM时出错: {e}")
        return ""


def _get_function_call_chains(repo_directory: str, query: str, snippet) -> List[str]:
    """获取函数调用链"""
    try:
        # 获取代码片段内容
        content = snippet.get_snippet(add_ellipsis=False, add_lines=False)

        # 识别代码片段中的主要函数
        main_function = _extract_main_function_from_snippet(content)
        if not main_function:
            return []

        # 构建调用图缓存路径
        dir_hash = hashlib.md5(repo_directory.encode()).hexdigest()
        call_graph_cache_path = os.path.join(CACHE_DIRECTORY, "main_call_graph", f"call_graph_{dir_hash}.json")

        # 获取函数调用链
        if os.path.exists(call_graph_cache_path):
            # 将相对路径转换为绝对路径
            if snippet.file_path.startswith('/'):
                full_file_path = snippet.file_path
            else:
                full_file_path = os.path.join(repo_directory, snippet.file_path)

            call_chains = get_function_call_chain(
                repo_directory=repo_directory,
                function_name=main_function,
                file_path=full_file_path,
                cache_path=call_graph_cache_path
            )

            # 过滤掉只包含函数本身的调用链（没有实际调用关系）
            filtered_chains = []
            for chain in call_chains:
                # 分割调用链，如果只有一个节点（函数本身），则跳过
                nodes = chain.split(" -> ")
                if len(nodes) > 1:
                    filtered_chains.append(chain)

            return filtered_chains
        else:
            logger.warning(f"调用图缓存文件不存在: {call_graph_cache_path}")
            return []

    except Exception as e:
        logger.warning(f"获取函数调用链失败: {e}")
        return []


def _convert_to_relative_path(file_path: str, repo_directory: str) -> str:
    """将绝对路径转换为相对于repo_directory的相对路径"""
    try:
        # 确保repo_directory是绝对路径
        repo_abs = os.path.abspath(repo_directory)

        # 如果file_path已经是相对路径，直接返回
        if not os.path.isabs(file_path):
            return file_path

        # 将绝对路径转换为相对路径
        file_abs = os.path.abspath(file_path)
        relative_path = os.path.relpath(file_abs, repo_abs)

        return relative_path
    except Exception:
        # 如果转换失败，返回原路径
        return file_path


def _convert_call_chains_to_relative(call_chains: List[str], repo_directory: str) -> List[str]:
    """将调用链中的绝对路径转换为相对路径"""
    converted_chains = []

    for chain in call_chains:
        # 分割调用链
        nodes = chain.split(" -> ")
        converted_nodes = []

        for node in nodes:
            # 分离文件路径和函数名 (格式: file_path:function_name)
            if ":" in node:
                file_path, function_name = node.rsplit(":", 1)
                relative_path = _convert_to_relative_path(file_path, repo_directory)
                converted_nodes.append(f"{relative_path}:{function_name}")
            else:
                converted_nodes.append(node)

        converted_chains.append(" -> ".join(converted_nodes))

    return converted_chains


def _extract_main_function_from_snippet(content: str) -> str:
    """从代码片段中提取主要函数名"""
    import re

    # 按优先级查找函数定义
    function_patterns = [
        # 导出的函数（最高优先级）
        (r'export\s+(?:const|let|var)\s+(\w+)\s*=', 1),
        (r'export\s+function\s+(\w+)\s*\(', 1),
        (r'export\s+\{\s*(\w+)', 1),

        # 常规函数定义
        (r'(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s+)?\(', 1),
        (r'(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s+)?function', 1),
        (r'function\s+(\w+)\s*\(', 1),

        # 方法定义
        (r'(\w+)\s*:\s*(?:async\s+)?function\s*\(', 1),
        (r'(\w+)\s*\([^)]*\)\s*\{', 1),

        # Python函数
        (r'def\s+(\w+)\s*\(', 1),
        (r'async\s+def\s+(\w+)\s*\(', 1),
    ]

    found_functions = []

    for pattern, group_idx in function_patterns:
        matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
        for match in matches:
            func_name = match.group(group_idx)
            if func_name and func_name not in ['if', 'for', 'while', 'switch', 'catch', 'return', 'throw']:
                # 检查是否是导出函数（优先级更高）
                is_exported = 'export' in match.group(0).lower()
                found_functions.append((func_name, is_exported))

    if not found_functions:
        return None

    # 优先返回导出的函数
    for func_name, is_exported in found_functions:
        if is_exported:
            return func_name

    # 如果没有导出函数，返回第一个找到的函数
    return found_functions[0][0]


