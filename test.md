# React Native表态功能优化UI测试用例

## 一、一级页面表态弹窗评分测试

### 测试场景1：默认评分修改验证
**前置条件：**
- 应用已安装并登录
- 进入包含表态功能的一级页面

**测试步骤：**
1. 点击表态按钮，弹出表态弹窗
2. 观察弹窗中评分的默认值

**预期结果：**
- 表态弹窗中默认评分值应为0分（而非原来的5分）

## 二、一级页面表态展示区测试

### 测试场景2：未开分且表态人数为0时的展示
**前置条件：**
- 页面状态为未开分
- 表态人数为0
- 首次进入该页面（未展示过引导气泡）

**测试步骤：**
1. 进入一级页面
2. 观察表态区域显示状态

**预期结果：**
- 页面应显示引导气泡
- 不应显示表态区

### 测试场景3：引导气泡仅展示一次的验证
**前置条件：**
- 页面状态为未开分
- 表态人数为0
- 已经查看过一次引导气泡

**测试步骤：**
1. 退出页面后再次进入同一页面
2. 观察是否再次出现引导气泡

**预期结果：**
- 引导气泡不应再次出现
- 本地存储应有相关记录标识已展示过引导气泡

### 测试场景4：未开分但表态人数≥1时的展示
**前置条件：**
- 页面状态为未开分
- 表态人数≥1

**测试步骤：**
1. 进入一级页面
2. 观察表态区域显示状态

**预期结果：**
- 表态区应显示"暂无评分"文本
- 文字样式应符合规范：字体PingFang SC，字重500，字号16px，行高24px，字间距0%，文字居中，颜色#FFFFFF
- 五颗星不应显示
- 右侧五星进度条应显示但不高亮

### 测试场景5：已开分且有表态时的展示
**前置条件：**
- 页面状态为已开分
- 表态人数≥1

**测试步骤：**
1. 进入一级页面
2. 观察表态区域显示状态

**预期结果：**
- 表态区应显示具体评分
- 五星进度条应高亮显示，反映当前评分等级

## 三、一级页面交互逻辑测试

### 测试场景6：表态区点击跳转
**前置条件：**
- 表态区已显示（无论是"有评分"还是"暂无评分"状态）

**测试步骤：**
1. 点击表态区
2. 观察页面跳转行为

**预期结果：**
- 应成功跳转至二级详情页面

## 四、二级页面表态详情页测试

### 测试场景7：未开分有表态的二级页展示
**前置条件：**
- 页面状态为未开分
- 已有用户进行表态

**测试步骤：**
1. 从一级页面点击进入二级详情页
2. 观察顶部表态区展示状态

**预期结果：**
- 顶部应显示"暂无评分"的表态区
- "暂无评分"文字颜色应为黑色(#000000)
- 五星进度条应不高亮

### 测试场景8：已开分有表态的二级页展示
**前置条件：**
- 页面状态为已开分
- 已有用户进行表态

**测试步骤：**
1. 从一级页面点击进入二级详情页
2. 观察顶部表态区展示状态

**预期结果：**
- 顶部应显示具体评分的表态区
- 五星进度条应高亮显示，反映当前评分等级
