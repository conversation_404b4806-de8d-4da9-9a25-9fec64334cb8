"""
缓存路径工具

统一管理缓存路径生成逻辑，避免路径生成代码重复。
"""

import os
from typing import Optional

from config.server import CACHE_DIRECTORY
from config.cache_constants import (
    CacheType, CacheNames, normalize_cache_type,
    get_cache_dir_name, EXTENSION_CACHE_BASE_DIR
)


def get_cache_base_path(cache_type: CacheType = CacheType.MAIN) -> str:
    """
    获取缓存基础路径
    
    参数:
        cache_type: 缓存类型
        
    返回:
        缓存基础路径
    """
    cache_type = normalize_cache_type(cache_type)
    
    if cache_type == CacheType.EXTENSION:
        return os.path.join(CACHE_DIRECTORY, EXTENSION_CACHE_BASE_DIR)
    else:
        return CACHE_DIRECTORY


def get_cache_directory_path(cache_name: CacheNames, cache_type: CacheType = CacheType.MAIN) -> str:
    """
    获取缓存目录路径
    
    参数:
        cache_name: 缓存名称
        cache_type: 缓存类型
        
    返回:
        缓存目录路径
    """
    base_path = get_cache_base_path(cache_type)
    cache_dir_name = get_cache_dir_name(cache_name)
    return os.path.join(base_path, cache_dir_name)


def get_cache_file_path(cache_name: CacheNames, cache_key: str, cache_type: CacheType = CacheType.MAIN) -> str:
    """
    获取缓存文件路径
    
    参数:
        cache_name: 缓存名称
        cache_key: 缓存键
        cache_type: 缓存类型
        
    返回:
        缓存文件路径
    """
    cache_dir = get_cache_directory_path(cache_name, cache_type)
    return os.path.join(cache_dir, cache_key)


def get_lexical_index_cache_path(cache_key: str, cache_type: CacheType = CacheType.MAIN) -> str:
    """
    获取词法索引缓存路径
    
    参数:
        cache_key: 缓存键
        cache_type: 缓存类型
        
    返回:
        词法索引缓存路径
    """
    return get_cache_file_path(CacheNames.LEXICAL_INDEX, cache_key, cache_type)


def get_debug_directory_path(cache_type: CacheType = CacheType.MAIN) -> str:
    """
    获取调试文件目录路径
    
    参数:
        cache_type: 缓存类型
        
    返回:
        调试文件目录路径
    """
    base_path = get_cache_base_path(cache_type)
    return os.path.join(base_path, "debug")


def ensure_cache_directory_exists(cache_name: CacheNames, cache_type: CacheType = CacheType.MAIN) -> str:
    """
    确保缓存目录存在，如果不存在则创建
    
    参数:
        cache_name: 缓存名称
        cache_type: 缓存类型
        
    返回:
        缓存目录路径
    """
    cache_dir = get_cache_directory_path(cache_name, cache_type)
    os.makedirs(cache_dir, exist_ok=True)
    return cache_dir


def ensure_debug_directory_exists(cache_type: CacheType = CacheType.MAIN) -> str:
    """
    确保调试目录存在，如果不存在则创建
    
    参数:
        cache_type: 缓存类型
        
    返回:
        调试目录路径
    """
    debug_dir = get_debug_directory_path(cache_type)
    os.makedirs(debug_dir, exist_ok=True)
    return debug_dir


def validate_cache_directories():
    """
    验证并创建所有必需的缓存目录
    """
    # 创建主仓库缓存目录
    for cache_name in CacheNames:
        ensure_cache_directory_exists(cache_name, CacheType.MAIN)
    ensure_debug_directory_exists(CacheType.MAIN)
    
    # 创建扩展源缓存目录
    for cache_name in CacheNames:
        ensure_cache_directory_exists(cache_name, CacheType.EXTENSION)
    ensure_debug_directory_exists(CacheType.EXTENSION)


# 向后兼容的函数
def get_cache_path(cache_type: str, cache_name: str, cache_key: Optional[str] = None) -> str:
    """
    向后兼容的缓存路径获取函数
    
    参数:
        cache_type: 缓存类型字符串
        cache_name: 缓存名称字符串
        cache_key: 可选的缓存键
        
    返回:
        缓存路径
    """
    cache_type_enum = normalize_cache_type(cache_type)
    
    # 尝试将字符串转换为枚举
    cache_name_enum = None
    for name in CacheNames:
        if name.value == cache_name:
            cache_name_enum = name
            break
    
    if cache_name_enum is None:
        # 如果没找到对应的枚举，使用字符串构建路径
        base_path = get_cache_base_path(cache_type_enum)
        cache_dir_name = f"{cache_name}_cache"
        cache_path = os.path.join(base_path, cache_dir_name)
        if cache_key:
            cache_path = os.path.join(cache_path, cache_key)
        return cache_path
    
    if cache_key:
        return get_cache_file_path(cache_name_enum, cache_key, cache_type_enum)
    else:
        return get_cache_directory_path(cache_name_enum, cache_type_enum)
