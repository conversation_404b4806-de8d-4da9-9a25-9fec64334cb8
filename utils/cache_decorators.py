"""
缓存操作装饰器

提供统一的缓存操作错误处理机制。
"""

import functools
from typing import Any, Callable, Optional
from loguru import logger


def safe_cache_operation(default_return: Any = None, log_errors: bool = True):
    """
    安全缓存操作装饰器
    
    参数:
        default_return: 操作失败时的默认返回值
        log_errors: 是否记录错误日志
        
    返回:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if log_errors:
                    logger.warning(f"缓存操作失败: {func.__name__}, 参数: {args}, {kwargs}, 错误: {e}")
                return default_return
        return wrapper
    return decorator


def safe_cache_get(default_return: Any = None):
    """
    安全缓存读取装饰器
    
    参数:
        default_return: 读取失败时的默认返回值
        
    返回:
        装饰器函数
    """
    return safe_cache_operation(default_return=default_return, log_errors=True)


def safe_cache_set(default_return: bool = False):
    """
    安全缓存写入装饰器
    
    参数:
        default_return: 写入失败时的默认返回值
        
    返回:
        装饰器函数
    """
    return safe_cache_operation(default_return=default_return, log_errors=True)


def cache_operation_with_fallback(fallback_func: Optional[Callable] = None):
    """
    带回退机制的缓存操作装饰器
    
    参数:
        fallback_func: 主操作失败时的回退函数
        
    返回:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(f"缓存操作失败: {func.__name__}, 尝试回退方案, 错误: {e}")
                if fallback_func:
                    try:
                        return fallback_func(*args, **kwargs)
                    except Exception as fallback_error:
                        logger.error(f"回退方案也失败: {fallback_func.__name__}, 错误: {fallback_error}")
                return None
        return wrapper
    return decorator


def retry_cache_operation(max_retries: int = 3, delay: float = 0.1):
    """
    重试缓存操作装饰器
    
    参数:
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
        
    返回:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.debug(f"缓存操作失败，第 {attempt + 1} 次重试: {func.__name__}, 错误: {e}")
                        if delay > 0:
                            import time
                            time.sleep(delay)
                    else:
                        logger.warning(f"缓存操作重试 {max_retries} 次后仍失败: {func.__name__}, 最后错误: {e}")
            
            return None
        return wrapper
    return decorator


class CacheOperationContext:
    """
    缓存操作上下文管理器
    
    提供统一的缓存操作错误处理和日志记录。
    """
    
    def __init__(self, operation_name: str, log_success: bool = False, log_errors: bool = True):
        """
        初始化上下文管理器
        
        参数:
            operation_name: 操作名称
            log_success: 是否记录成功日志
            log_errors: 是否记录错误日志
        """
        self.operation_name = operation_name
        self.log_success = log_success
        self.log_errors = log_errors
        self.success = False
        self.error = None
    
    def __enter__(self):
        """进入上下文"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if exc_type is None:
            self.success = True
            if self.log_success:
                logger.debug(f"缓存操作成功: {self.operation_name}")
        else:
            self.success = False
            self.error = exc_val
            if self.log_errors:
                logger.warning(f"缓存操作失败: {self.operation_name}, 错误: {exc_val}")
        
        # 返回 True 表示异常已处理，不会向上传播
        return True
    
    def is_success(self) -> bool:
        """检查操作是否成功"""
        return self.success
    
    def get_error(self) -> Optional[Exception]:
        """获取错误信息"""
        return self.error
