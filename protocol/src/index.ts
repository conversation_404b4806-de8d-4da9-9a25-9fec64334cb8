import { ToCoreFromIdeProtocol, ToIdeFromCoreProtocol } from "./ideCore.js";
import { IProtocol } from "./messenger/index.js";

// Re-export IProtocol
export { IProtocol };

// IDE
export type ToIdeProtocol = ToIdeFromCoreProtocol;
export type FromIdeProtocol = ToCoreFromIdeProtocol;

// Core
export type ToCoreProtocol = ToCoreFromIdeProtocol;
export type FromCoreProtocol = ToIdeFromCoreProtocol;

// Export all from submodules
export * from "./core.js";
export * from "./ide.js";
export * from "./ideCore.js";
export * from "./messenger/index.js"; 