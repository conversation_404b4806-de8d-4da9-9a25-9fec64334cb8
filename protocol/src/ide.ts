export interface IDE {
  getWorkspaceDirs(): Promise<string[]>;
  getBranch(dir: string): Promise<string>;
  getRepoName(dir: string): Promise<string | undefined>;
  getCodebaseExtensions(): Promise<string[]>;
}

export type ToIdeFromWebviewOrCoreProtocol = {
  // Methods from IDE type
  getWorkspaceDirs: [undefined, string[]];
  getBranch: [{ dir: string }, string];
  getRepoName: [{ dir: string }, string | undefined];
  getCodebaseExtensions: [undefined, string[]];

  // Custom MCP messages
  "files/created/response": [{ 
    success: boolean; 
    message: string; 
    processedFiles: string[] 
  }, void];
};

export type ToWebviewOrCoreFromIdeProtocol = {
  "files/created": [{ uris?: string[] }, void];
}; 