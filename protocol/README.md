# Protocol

This package contains the protocol interfaces and types used for communication between different parts of the codemind project.

## Structure

- `core.ts`: Core protocol communication types
- `ide.ts`: IDE-related interfaces and types
- `ideCore.ts`: Communication protocol between IDE and Core
- `messenger/`: Implementation of the messaging system

## Usage

To use in your project:

```bash
npm install ../protocol
```

Or add as a local dependency in your package.json:

```json
{
  "dependencies": {
    "protocol": "file:../protocol"
  }
}
```

Then import in your code:

```typescript
import { IDE, IMessenger, InProcessMessenger } from 'protocol';
```

## Development

Run `npm run build` to build the package. 