# 环境说明
## python 环境
执行下面命令：
```
python3 -m venv .venv
source .venv/bin/activate
pip3 install -r requirements.txt
```

# API 使用说明
## 启动服务
执行命令：
```
python codemind_api.py [--host 0.0.0.0] [--port 8088] [--reload] [--workers 1]
```

服务器启动后，可通过以下地址访问：
- API 接口: http://[host]:[port]
- API 文档: http://[host]:[port]/docs
- OpenAPI 规范: http://[host]:[port]/openapi.json

默认地址
http://0.0.0.0:8088

## API 接口
服务提供以下 HTTP 接口:

### 索引代码库 (POST /index)
```
POST /index
Content-Type: application/json

{
  "repo_directory": "/path/to/your/repo",
  "force_reindex": false,
  "extension_sources": "可选，扩展源JSON数组字符串"
}
```

### 代码库搜索 (POST /codebase_search)
```
POST /codebase_search
Content-Type: application/json

{
  "repo_directory": "/path/to/your/repo",
  "query": "your search query",
  "limit": 10,
  "min_score": 0
}
```

### Grep搜索 (POST /grep_search)
```
POST /grep_search
Content-Type: application/json

{
  "repo_directory": "/path/to/your/repo",
  "query": "your grep query",
  "limit": 10
}
```

### 知识库搜索 (POST /knowledge_search)
```
POST /knowledge_search
Content-Type: application/json

{
  "query": "your search query",
  "limit": 10,
  "timeout": 60
}
```

### 扩展源搜索 (POST /extension_search)
索引代码库时指定了`extension_sources`才生效
```
POST /extension_search
Content-Type: application/json

{
  "repo_directory": "/path/to/your/repo",
  "query": "your search query",
  "limit": 10,
  "min_score": 0
}
```