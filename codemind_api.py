"""
CodeMind API - 提供代码库索引和搜索功能的FastAPI接口

本模块提供几个主要接口：
1. 触发代码块分割和嵌入生成 (index_codebase)
2. 进行代码库语义搜索 (codebase_search)
3. 进行代码库Grep搜索 (grep_search)
4. 知识库搜索 (knowledge_search)
5. 扩展源搜索 (extension_search)
6. 扩展源索引状态查询 (extension_status)
7. 获取缓存目录路径 (cache_directory)

注意：
- repo_directory 索引时不再使用 LLM 生成摘要
- extension_sources 在后台异步构建索引和向量化（包含 LLM 摘要）
- extension_sources 的索引存储在 CACHE_DIRECTORY/extension_dir 目录
"""

import logging
from typing import Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import uvicorn

# 导入API功能
from api import (
    index_codebase,
    codebase_search,
    grep_search,
    knowledge_search,
    extension_search
)

# 导入配置
from config.server import CACHE_DIRECTORY

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="CodeMind API",
    description="代码库索引和搜索功能的API接口",
    version="1.0.0"
)

# 请求模型定义
class IndexRequest(BaseModel):
    repo_directory: str = Field(..., description="代码库目录路径")
    version_id: Optional[str] = Field(None, description="版本标识符（可选）")
    force_reindex: bool = Field(False, description="是否强制重新索引（可选）")
    extension_sources: Optional[str] = Field(None, description="扩展源JSON数组字符串（可选），包含本地文件夹路径或远程Git仓库地址")
    llm_summary: bool = Field(False, description="是否使用LLM生成索引摘要（可选）")
    contextual_retrieval: bool = Field(False, description="是否生成 chunk 上下文（可选）")

class CodebaseSearchRequest(BaseModel):
    repo_directory: str = Field(..., description="代码库目录路径")
    query: str = Field(..., description="搜索查询")
    version_id: Optional[str] = Field(None, description="版本标识符（可选）")
    limit: int = Field(5, description="返回结果数量限制（可选，默认5）")
    use_rerank: bool = Field(False, description="是否使用rerank步骤（可选）")
    min_score: float = Field(0.5, description="最低相关度阈值（可选，默认0.5）")
    complete_imports: bool = Field(False, description="是否补全导入语句（可选）")
    call_graph: bool = Field(False, description="是否显示函数调用链（可选）")
    use_llm_filter: bool = Field(True, description="是否使用LLM过滤不相关结果（默认启用）")

class GrepSearchRequest(BaseModel):
    repo_directory: str = Field(..., description="代码库目录路径")
    query: str = Field(..., description="搜索关键词或正则表达式")
    context_lines: int = Field(5, description="匹配行前后显示的上下文行数（可选，默认5）")
    limit: int = Field(5, description="返回结果数量限制（可选，默认5）")

class KnowledgeSearchRequest(BaseModel):
    query: str = Field(..., description="搜索查询")
    limit: int = Field(3, description="返回结果数量限制（可选，默认3）")
    timeout: int = Field(30, description="请求超时时间（秒，可选，默认30）")

class ExtensionSearchRequest(BaseModel):
    repo_directory: str = Field(..., description="代码库目录路径")
    query: str = Field(..., description="搜索查询")
    limit: int = Field(5, description="返回结果数量限制（可选，默认5）")
    use_rerank: bool = Field(False, description="是否使用rerank步骤（可选）")
    min_score: float = Field(0.5, description="最低相关度阈值（可选，默认0.5）")

# 响应模型定义
class APIResponse(BaseModel):
    success: bool = Field(..., description="请求是否成功")
    message: dict = Field(..., description="响应消息内容")
    error: Optional[str] = Field(None, description="错误信息（如果有）")

class CacheDirectoryResponse(BaseModel):
    success: bool = Field(..., description="请求是否成功")
    cache_directory: str = Field(..., description="缓存目录路径")
    error: Optional[str] = Field(None, description="错误信息（如果有）")

@app.post("/index", response_model=APIResponse)
async def index_endpoint(request: IndexRequest):
    """索引代码库"""
    try:
        result = index_codebase(
            repo_directory=request.repo_directory,
            force_reindex=request.force_reindex,
            extension_sources=request.extension_sources,
            llm_summary=request.llm_summary,
            enable_contextual_retrieval=request.contextual_retrieval
        )

        formatted_message = f"索引结果: {result}"

        return APIResponse(
            success=True,
            message={"message": formatted_message}
        )
    except Exception as e:
        logger.error(f"索引失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/codebase_search", response_model=APIResponse)
async def codebase_search_endpoint(request: CodebaseSearchRequest):
    """搜索代码库"""
    try:
        search_results = codebase_search(
            repo_directory=request.repo_directory,
            query=request.query,
            limit=request.limit,
            use_rerank=request.use_rerank,
            min_score=request.min_score,
            complete_imports=request.complete_imports,
            call_graph=request.call_graph,
            use_llm_filter=request.use_llm_filter
        )

        formatted_lines = []
        formatted_lines.append(f"搜索结果状态: {search_results['code']} - {search_results['msg']}")
        formatted_lines.append(f"搜索耗时: {search_results['cost_time']:.2f} 秒")
        formatted_lines.append(f"搜索结果数量: {len(search_results['data'])}")

        for i, result_item in enumerate(search_results['data']):
            formatted_lines.append(f"结果 {i+1}:")
            formatted_lines.append(f"  查询: {result_item['query']}")
            formatted_lines.append(f"  文件信息: {result_item['file_path']}[{result_item['start']}-{result_item['end']}]")
            formatted_lines.append(f"  内容片段: {result_item['passage']}")

            if 'call_chains' in result_item and result_item['call_chains']:
                formatted_lines.append(f"  函数调用链 ({len(result_item['call_chains'])} 条):")
                for j, chain in enumerate(result_item['call_chains']):
                    formatted_lines.append(f"    调用链 {j+1}: {chain}")
                formatted_lines.append("")

        formatted_message = "\n".join(formatted_lines)

        return APIResponse(
            success=True,
            message={"message": formatted_message}
        )
    except Exception as e:
        logger.error(f"代码库搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/grep_search", response_model=APIResponse)
async def grep_search_endpoint(request: GrepSearchRequest):
    """使用grep在代码库中搜索关键词"""
    try:
        grep_results = grep_search(
            repo_directory=request.repo_directory,
            query=request.query,
            context_lines=request.context_lines,
            limit=request.limit
        )

        formatted_lines = []
        formatted_lines.append(f"搜索结果状态: {grep_results['code']} - {grep_results['msg']}")
        formatted_lines.append(f"搜索耗时: {grep_results['cost_time']:.2f} 秒")
        if grep_results['code'] == 0:
            formatted_lines.append(f"搜索结果数量: {len(grep_results['data'])}")
            for i, result_item in enumerate(grep_results['data'][:request.limit]):
                formatted_lines.append(f"结果 {i+1}:")
                formatted_lines.append(f"  查询: {result_item['query']}")
                formatted_lines.append(f"  匹配次数: {result_item['score']}")
                formatted_lines.append(f"  文件信息: {result_item['file_path']}[{result_item['start']}-{result_item['end']}]")

        formatted_message = "\n".join(formatted_lines)

        return APIResponse(
            success=True,
            message={"message": formatted_message}
        )
    except Exception as e:
        logger.error(f"Grep搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/knowledge_search", response_model=APIResponse)
async def knowledge_search_endpoint(request: KnowledgeSearchRequest):
    """使用知识库进行搜索"""
    try:
        knowledge_results = knowledge_search(
            query=request.query,
            limit=request.limit,
            timeout=request.timeout
        )

        formatted_lines = []
        formatted_lines.append(f"搜索结果状态: {knowledge_results['code']} - {knowledge_results['msg']}")
        formatted_lines.append(f"搜索耗时: {knowledge_results['cost_time']:.2f} 秒")
        if knowledge_results['code'] == 0 and 'replies' in knowledge_results['data']:
            formatted_lines.append(f"搜索结果数量: {len(knowledge_results['data']['replies'])}")
            formatted_lines.append(f"搜索结果: {knowledge_results['data']['replies']}")

        formatted_message = "\n".join(formatted_lines)

        return APIResponse(
            success=True,
            message={"message": formatted_message}
        )
    except Exception as e:
        logger.error(f"知识库搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/extension_search", response_model=APIResponse)
async def extension_search_endpoint(request: ExtensionSearchRequest):
    """搜索扩展源代码库"""
    try:
        extension_results = extension_search(
            repo_directory=request.repo_directory,
            query=request.query,
            limit=request.limit,
            use_rerank=request.use_rerank,
            min_score=request.min_score
        )

        formatted_lines = []
        formatted_lines.append(f"搜索结果状态: {extension_results['code']} - {extension_results['msg']}")
        formatted_lines.append(f"搜索耗时: {extension_results['cost_time']:.2f} 秒")
        if extension_results['code'] == 0:
            formatted_lines.append(f"搜索结果数量: {len(extension_results['data'])}")
            for i, result_item in enumerate(extension_results['data'][:request.limit]):
                formatted_lines.append(f"结果 {i+1}:")
                formatted_lines.append(f"  查询: {result_item['query']}")
                formatted_lines.append(f"  相关度: {result_item['score']}")
                formatted_lines.append(f"  文件信息: {result_item['file_path']}[{result_item['start']}-{result_item['end']}]")
                formatted_lines.append(f"  来源类型: {result_item.get('source_type', 'unknown')}")
                formatted_lines.append(f"  内容片段: {result_item['passage']}")

        formatted_message = "\n".join(formatted_lines)

        return APIResponse(
            success=True,
            message={"message": formatted_message}
        )
    except Exception as e:
        logger.error(f"扩展源搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/cache_directory", response_model=CacheDirectoryResponse)
async def get_cache_directory():
    """获取缓存目录路径"""
    try:
        return CacheDirectoryResponse(
            success=True,
            cache_directory=CACHE_DIRECTORY
        )
    except Exception as e:
        logger.error(f"获取缓存目录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="CodeMind FastAPI服务器")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8088, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开发模式，自动重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数量")

    args = parser.parse_args()

    print(f"启动CodeMind API服务器...")
    print(f"访问地址: http://{args.host}:{args.port}")
    print(f"API文档: http://{args.host}:{args.port}/docs")
    print(f"OpenAPI规范: http://{args.host}:{args.port}/openapi.json")

    uvicorn.run(
        "codemind_api:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers if not args.reload else 1
    )