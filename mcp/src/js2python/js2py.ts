import { CodeMindServiceManager } from './service-manager.js';


export class CodeMindBridge {
    private serviceManager: CodeMindServiceManager;

    public constructor(rootPath: string) {
        this.serviceManager = CodeMindServiceManager.getInstance(rootPath);
    }

    // 检索 codebase
    public async codebase_search(repoPath: string, query: string, limit: number = 10) {
        return this.serviceManager.codebase_search(repoPath, query, limit);
    }

    // 检索 grep
    public async grep_search(repoPath: string, query: string) {
        return this.serviceManager.grep_search(repoPath, query);
    }

    // 知识库搜索
    public async knowledge_search(query: string, limit: number = 10, timeout: number = 30) {
        return this.serviceManager.knowledge_search(query, limit, timeout);
    }

    // 扩展源搜索
    public async extension_search(repoPath: string, query: string) {
        return this.serviceManager.extension_search(repoPath, query);
    }

    // 构建索引
    public async prepare_search_index(repoPath: string, extensions?: string[], llmSummary?: boolean) {
        return this.serviceManager.prepare_search_index(repoPath, extensions, llmSummary);
    }
}

// Export the default bridge instance
export default CodeMindBridge;