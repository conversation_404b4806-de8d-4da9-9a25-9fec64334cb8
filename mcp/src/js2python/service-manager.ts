import axios, { AxiosInstance } from 'axios';
import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as os from 'os';
import { createHash } from 'crypto';

/**
 * 单例服务管理器，负责管理 FastAPI 后台服务和所有 API 调用
 */
export class CodeMindServiceManager {
    private static instance: CodeMindServiceManager | null = null;
    private projectRoot: string;
    private apiPath: string;
    private pythonExecutable: string;
    private serverProcess: ChildProcess | null = null;
    private serverPort: number = 8088;
    private serverHost: string = 'localhost';
    private isInitialized: boolean = false;
    private initializationPromise: Promise<void> | null = null;
    private httpClient: AxiosInstance;

    private constructor(rootPath: string) {
        this.projectRoot = rootPath;
        this.apiPath = path.join(this.projectRoot, 'codemind_api.py');
        this.pythonExecutable = path.join(this.projectRoot, '.venv/bin/python');

        this.setCacheDirectory(rootPath);

        this.httpClient = axios.create({
            baseURL: this.getBaseURL(),
            timeout: 60000,
            headers: {
                'Content-Type': 'application/json',
            },
        });

        this.checkAndStartService();
    }

    private setCacheDirectory(repoPath: string): void {
        const HOME_DIR = os.homedir();
        const repoPathMd5 = createHash('md5').update(repoPath).digest('hex');
        process.env.CACHE_DIRECTORY = path.join(HOME_DIR, `.codemind_caches/${repoPathMd5}`);
        console.log(`设置缓存目录: ${process.env.CACHE_DIRECTORY}`);
    }

    private async isServerRunning(): Promise<boolean> {
        try {
            await this.httpClient.get('/docs', { timeout: 2000 });
            return true;
        } catch (error) {
            return false;
        }
    }

    private checkAndStartService(): void {
        if (this.initializationPromise) {
            return;
        }

        this.initializationPromise = this.performInitialCheck();
    }

    private async performInitialCheck(): Promise<void> {
        try {
            const isRunning = await this.isServerRunning();
            if (isRunning) {
                console.log(`FastAPI 服务已在端口 ${this.serverPort} 运行，复用现有服务`);
                this.isInitialized = true;
            } else {
                console.log('FastAPI 服务未启动，正在启动新服务...');
                await this.initializeService();
            }
        } catch (error) {
            console.log('检查服务状态失败，启动新服务...', error);
            await this.initializeService();
        }
    }

    public static getInstance(rootPath: string): CodeMindServiceManager {
        if (!CodeMindServiceManager.instance) {
            CodeMindServiceManager.instance = new CodeMindServiceManager(rootPath);
        }
        
        return CodeMindServiceManager.instance;
    }



    private async initializeService(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        console.log('正在启动 FastAPI 服务...');
        await this.startServer();
        this.isInitialized = true;
        console.log('FastAPI 服务启动完成');
    }



    private async startServer(): Promise<void> {
        if (this.serverProcess) {
            console.log('FastAPI 服务已经在运行');
            return;
        }

        return new Promise((resolve, reject) => {
            try {
                console.log('启动 FastAPI 服务...');

                const env = {
                    ...process.env
                };

                this.serverProcess = spawn(this.pythonExecutable, [
                    this.apiPath,
                    '--host', this.serverHost,
                    '--port', this.serverPort.toString()
                ], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    cwd: this.projectRoot,
                    env: env
                });

                let serverStarted = false;

                this.serverProcess.stdout?.on('data', (data) => {
                    const output = data.toString();
                    console.log('FastAPI 输出:', output);

                    if (output.includes('Uvicorn running on') && !serverStarted) {
                        serverStarted = true;
                        setTimeout(() => {
                            this.waitForServerReady().then(resolve).catch(reject);
                        }, 1000);
                    }
                });

                this.serverProcess.stderr?.on('data', (data) => {
                    const output = data.toString();
                    console.log('FastAPI 输出:', output);

                    if (output.includes('Uvicorn running on') && !serverStarted) {
                        serverStarted = true;
                        setTimeout(() => {
                            this.waitForServerReady().then(resolve).catch(reject);
                        }, 1000);
                    }
                });

                this.serverProcess.on('error', (error) => {
                    console.error('启动 FastAPI 服务失败:', error);
                    reject(error);
                });

                this.serverProcess.on('exit', (code) => {
                    console.log(`FastAPI 服务退出，退出码: ${code}`);
                    this.serverProcess = null;
                    this.isInitialized = false;
                    this.initializationPromise = null;
                    if (!serverStarted) {
                        reject(new Error(`FastAPI 服务启动失败，退出码: ${code}`));
                    }
                });

                // setTimeout(() => {
                //     if (!serverStarted) {
                //         reject(new Error('FastAPI 服务启动超时'));
                //     }
                // }, 30000);

            } catch (error) {
                reject(error);
            }
        });
    }

    private async waitForServerReady(maxRetries: number = 10): Promise<void> {
        for (let i = 0; i < maxRetries; i++) {
            try {
                await this.httpClient.get('/docs');
                console.log('FastAPI 服务已就绪');
                return;
            } catch (error) {
                console.log(`等待服务就绪... (${i + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        throw new Error('FastAPI 服务健康检查失败');
    }

    public async stopServer(): Promise<void> {
        if (this.serverProcess) {
            console.log('停止 FastAPI 服务...');
            this.serverProcess.kill('SIGTERM');

            await new Promise<void>((resolve) => {
                if (this.serverProcess) {
                    this.serverProcess.on('exit', () => {
                        this.serverProcess = null;
                        this.isInitialized = false;
                        this.initializationPromise = null;
                        resolve();
                    });
                } else {
                    resolve();
                }
            });

            console.log('FastAPI 服务已停止');
        } else {
            // 即使没有进程在运行，也要重置状态
            this.serverProcess = null;
            this.isInitialized = false;
            this.initializationPromise = null;
            console.log('服务状态已重置');
        }
    }

    /**
     * 重启服务
     */
    private async restartService(): Promise<void> {
        console.log('检测到服务异常，正在重启服务...');

        // 停止现有服务
        await this.stopServer();

        // 等待一段时间确保端口释放
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 重置状态
        this.isInitialized = false;
        this.initializationPromise = null;

        // 检查端口是否已经有服务在运行
        const isRunning = await this.isServerRunning();
        if (isRunning) {
            console.log('发现端口上已有服务运行，复用现有服务');
            this.isInitialized = true;
            return;
        }

        // 启动新服务
        await this.initializeService();

        console.log('服务重启完成');
    }

    /**
     * 检查错误是否需要重启服务
     */
    private shouldRestartService(error: any): boolean {
        // 检查是否是网络连接错误
        if (error.code === 'ECONNREFUSED' ||
            error.code === 'ENOTFOUND' ||
            error.code === 'ETIMEDOUT' ||
            error.code === 'ECONNRESET') {
            return true;
        }

        // 检查 HTTP 状态码
        if (error.response) {
            const status = error.response.status;
            // 5xx 服务器错误可能需要重启
            if (status >= 500 && status < 600) {
                return true;
            }
        }

        // 检查错误消息
        const errorMessage = error.message?.toLowerCase() || '';
        if (errorMessage.includes('network error') ||
            errorMessage.includes('connection refused') ||
            errorMessage.includes('service unavailable') ||
            errorMessage.includes('timeout')) {
            return true;
        }

        return false;
    }

    /**
     * 带重试机制的 API 调用包装器
     */
    private async executeWithRetry<T>(
        apiCall: () => Promise<T>,
        methodName: string,
        maxRetries: number = 1
    ): Promise<T> {
        let lastError: any;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                await this.waitForServiceReady();
                return await apiCall();
            } catch (error) {
                lastError = error;
                console.error(`${methodName} 调用失败 (尝试 ${attempt + 1}/${maxRetries + 1}):`, error);

                // 如果不是最后一次尝试且错误需要重启服务
                if (attempt < maxRetries && this.shouldRestartService(error)) {
                    try {
                        await this.restartService();
                        console.log(`${methodName} 服务重启后准备重试...`);
                    } catch (restartError) {
                        console.error(`${methodName} 服务重启失败:`, restartError);
                        // 如果重启失败，直接抛出原始错误
                        throw lastError;
                    }
                } else {
                    // 最后一次尝试失败或不需要重启，直接抛出错误
                    throw lastError;
                }
            }
        }

        throw lastError;
    }

    public getBaseURL(): string {
        return `http://${this.serverHost}:${this.serverPort}`;
    }

    public isServiceReady(): boolean {
        return this.isInitialized;
    }

    /**
     * 等待服务完全准备好
     * 这个方法确保服务已经启动并可以接受请求
     * 这是调用任何 API 方法之前的最佳实践
     */
    public async waitForServiceReady(): Promise<void> {
        // 如果服务已经初始化，直接返回
        if (this.isInitialized) {
            return;
        }

        // 如果有初始化 Promise，等待它完成
        if (this.initializationPromise) {
            await this.initializationPromise;
            return;
        }

        // 如果没有初始化 Promise 且服务未就绪，启动初始化
        if (!this.isInitialized && !this.initializationPromise) {
            console.log('服务未就绪，开始初始化...');
            this.checkAndStartService();

            // 等待初始化完成
            if (this.initializationPromise) {
                await this.initializationPromise;
            }
        }

        // 最终检查：如果服务仍未准备好，抛出错误
        if (!this.isInitialized) {
            throw new Error('服务初始化失败，无法准备就绪');
        }
    }

    public async codebase_search(
        repoPath: string,
        query: string,
        limit: number = 10
    ) {
        return this.executeWithRetry(async () => {
            const response = await this.httpClient.post('/codebase_search', {
                repo_directory: repoPath,
                query: query,
                limit: limit,
                min_score: 0.0
            });

            return response.data.message.message;
        }, 'codebase_search');
    }

    public async grep_search(
        repoPath: string,
        query: string
    ) {
        return this.executeWithRetry(async () => {
            const response = await this.httpClient.post('/grep_search', {
                repo_directory: repoPath,
                query: query
            });

            return response.data.message.message;
        }, 'grep_search');
    }

    public async knowledge_search(
        query: string,
        limit: number = 10,
        timeout: number = 30
    ) {
        return this.executeWithRetry(async () => {
            const response = await this.httpClient.post('/knowledge_search', {
                query: query,
                limit: limit,
                timeout: timeout
            });

            return response.data.message.message;
        }, 'knowledge_search');
    }

    public async extension_search(
        repoPath: string,
        query: string
    ) {
        return this.executeWithRetry(async () => {
            const response = await this.httpClient.post('/extension_search', {
                repo_directory: repoPath,
                query: query
            });

            return response.data.message.message;
        }, 'extension_search');
    }

    public async prepare_search_index(
        repoPath: string,
        extensions?: string[],
        llmSummary?: boolean,
        contextualRetrieval?: boolean
    ) {
        return this.executeWithRetry(async () => {
            let extensionSources: string | undefined;

            if (extensions) {
                try {
                    if (Array.isArray(extensions) && extensions.length > 0) {
                        extensionSources = JSON.stringify(extensions);
                    }
                } catch (error) {
                    console.error(`Error reading or parsing extensions: ${extensions}`, error);
                }
            }

            const response = await this.httpClient.post('/index', {
                repo_directory: repoPath,
                extension_sources: extensionSources,
                llm_summary: llmSummary || false,
                contextual_retrieval: contextualRetrieval || false
            });

            return response.data.message.message;
        }, 'prepare_search_index');
    }
}