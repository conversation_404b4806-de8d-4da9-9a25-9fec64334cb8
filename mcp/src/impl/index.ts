import { z } from "zod";
import axios from 'axios';
import { CodeMindBridge } from "../js2python/index.js";
import { currentWorkingDirectory } from "./env.js";
import path from "path";
import { fileURLToPath } from "url";

/*
curl --request POST \
  --url https://aiplat-gateway.devops.beta.xiaohongshu.com/allin-workflow-codemindbridges/pipelines/main \
  --header 'content-type: application/json' \
  --header 'APP_ID: codemindbridges' \
  --header 'APP_KEY: o8PzmCjD8E/N6L6BGzSq8ODQRZ/OD7aOECR1xX6SPZw=' \
  --data '{"query": "hello"}'
*/


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '../../../');

const bridge = new CodeMindBridge(projectRoot);

// 配置参数
const MCP_CONFIG = {
  HOST: "https://aiplat-gateway.devops.beta.xiaohongshu.com",
  APP_ID: "codemindbridges",
  APP_KEY: "o8PzmCjD8E/N6L6BGzSq8ODQRZ/OD7aOECR1xX6SPZw=",
  BUSINESS_NAME: "allin-workflow-codemindbridges",
  TIMEOUT: 20000 // 20秒超时
};

const TOOL_CONST = {
  REACT_NATIVE_KNOWLEDGE_SEARCH: "reactnative-knowledge-search",
  CODEBASE_SEARCH: "codebase-search",
  GREP_SEARCH: "grep-search",
  EXTENSION_SEARCH: "extension-search"
};

// 定义 Zod 校验模式
const SearchArgumentsSchema = z.object({
  query: z.string().min(1, "Query cannot be empty"),
});


// 调用 MCP 检索服务
async function callRemoteSearchService(query: string, chat_history: any[] = []) {
  try {
    const response = await axios.post(
      `${MCP_CONFIG.HOST}/${MCP_CONFIG.BUSINESS_NAME}/pipelines/main`,
      { query, chat_history },
      {
        headers: {
          "APP_ID": MCP_CONFIG.APP_ID,
          "APP_KEY": MCP_CONFIG.APP_KEY
        },
        timeout: MCP_CONFIG.TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    console.error("MCP service error:", error);
    throw new Error("Failed to call MCP service");
  }
}

// 格式化检索结果
function formatSearchResults(data: any) {
  if (!data || !data.replies) return "No results found";
  // 这里要处理下, 有可能是 component 也有可能是 bridge. 结构不同
  return data.replies.map((result: any) => {

    if (result.componentName) {
      return [
        `componentName: ${result.componentName || 'N/A'}`,
        `componentName_CN: ${result.componentNameCn || 'N/A'}`,
        `props: ${result.props || 'N/A'}`,
        `events: ${result.events || 'N/A'}`,
        `snippets: ${result.snippets || 'N/A'}`,
        `componentDesc: ${result.componentDesc || 'N/A'}`,
        "-----------"
      ].join("\n");
    } else {
      return [
        `MethodName: ${result.MethodName || 'N/A'}`,
        `MethodDesc: ${result.MethodDesc || 'N/A'}`,
        `InputArgs: ${result.InputArgs || 'N/A'}`,
        `OutputResponse: ${result.OutputResponse || 'N/A'}`,
        `Snippets: ${result.Snippets || 'N/A'}`,
        `score: ${result.score || 'N/A'}`,
        "-----------"
      ].join("\n");
    }
  }).join("\n\n");
  // return JSON.stringify(data)
}

export function getTools() {
  return [
    {
      name: TOOL_CONST.REACT_NATIVE_KNOWLEDGE_SEARCH,
      description: "Search the React Native knowledge base for detailed explanations, best practices, and example code that semantically match the user's query. This is a semantic search tool, so use the user's exact phrasing whenever possible. If codebase_search returns incomplete or missing information, reactnative-knowledge-search should step in to fill in those gaps via the organized knowledge base.",
      inputSchema: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The search query to find relevant code. You should reuse the user's exact query/most recent message with their wording unless there is a clear reason not to."
          }
        },
        required: ["query"]
      }
    },
    {
      name: TOOL_CONST.CODEBASE_SEARCH,
      description: "Find snippets of code from the codebase most relevant to the search query.\\nThis is a semantic search tool, so the query should ask for something semantically matching what is needed.\\nIf it makes sense to only search in particular directories, please specify them in the target_directories field.\\nUnless there is a clear reason to use your own search query, please just reuse the user's exact query with their wording.\\nTheir exact wording/phrasing can often be helpful for the semantic search query. Keeping the same exact question format can also be helpful.",
      inputSchema: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The search query to find relevant code. You should reuse the user's exact query/most recent message with their wording unless there is a clear reason not to."
          }
        },
        required: ["query"]
      }
    },
    {
      name: TOOL_CONST.GREP_SEARCH,
      description: "Fast text-based regex search that finds exact pattern matches within files or directories, utilizing the ripgrep command for efficient searching.\\nResults will be formatted in the style of ripgrep and can be configured to include line numbers and content.\\nTo avoid overwhelming output, the results are capped at 50 matches.\\nUse the include or exclude patterns to filter the search scope by file type or specific paths.\\n\\nThis is best for finding exact text matches or regex patterns.\\nMore precise than semantic search for finding specific strings or patterns.\\nThis is preferred over semantic search when we know the exact symbol/function name/etc. to search in some set of directories/file types.",
      inputSchema: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The regex pattern to search for"
          }
        },
        required: ["query"]
      }
    },
    {
      name: TOOL_CONST.EXTENSION_SEARCH,
      description: "Search extension sources (external libraries, frameworks, or additional codebases) for relevant code snippets. Extension sources are indexed with LLM-generated summaries for better semantic understanding.\\nThis tool searches through extension sources that have been configured and indexed separately from the main codebase.\\nUse this when you need to find code examples or implementations from external sources or additional repositories.",
      inputSchema: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The search query to find relevant code from extension sources. You should reuse the user's exact query/most recent message with their wording unless there is a clear reason not to."
          }
        },
        required: ["query"]
      }
    }
  ]
}

export function getToolHandlers() {
  return {
    [TOOL_CONST.REACT_NATIVE_KNOWLEDGE_SEARCH]: async (args: any) => {
      // 验证参数
      const { query } = SearchArgumentsSchema.parse(args);
      const chat_history: any[] = []
      // 设置30秒超时，返回10条结果
      const response = await bridge.knowledge_search(query || "N/A", 10, 30);

      return {
        content: [
          {
            type: "text",
            text: `codebase result:\n\n${response}`
          }
        ]
      };
    },
    [TOOL_CONST.CODEBASE_SEARCH]: async (args: any) => {
      // 验证参数
      const { query } = SearchArgumentsSchema.parse(args);
      const chat_history: any[] = []
      const response = await bridge.codebase_search(currentWorkingDirectory, query || "N/A", 15);

      return {
        content: [
          {
            type: "text",
            text: `codebase result:\n\n${response}`
          }
        ]
      };
    },
    [TOOL_CONST.GREP_SEARCH]: async (args: any) => {
      // 验证参数
      const { query } = SearchArgumentsSchema.parse(args);
      const chat_history: any[] = []
      const response = await bridge.grep_search(currentWorkingDirectory, query || "N/A");
      return {
        content: [
          {
            type: "text",
            text: `grep result:\n\n${response}`
          }
        ]
      };
    },
    [TOOL_CONST.EXTENSION_SEARCH]: async (args: any) => {
      // 验证参数
      const { query } = SearchArgumentsSchema.parse(args);
      const chat_history: any[] = []
      const response = await bridge.extension_search(currentWorkingDirectory, query || "N/A");
      return {
        content: [
          {
            type: "text",
            text: `extension search result:\n\n${response}`
          }
        ]
      };
    }
  }
}

