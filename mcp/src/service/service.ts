// import type { FromCoreProtocol, ToCoreProtocol } from "./protocol";
import { error } from "console";
import { CodeMindBridge } from "../js2python/js2py.js";
import { IMessenger, Message } from "protocol";
import type { FromCoreProtocol, ToCoreProtocol } from "protocol";
import type { IDE } from "protocol";

export class CodeMindService {
  private readonly bridgeInstance: CodeMindBridge;
  public get bridge(): Readonly<CodeMindBridge> {
    return this.bridgeInstance;
  }

  constructor(
    private readonly messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    private readonly ide: IDE,
    private readonly rootPath: string,
  ) {
    this.bridgeInstance = new CodeMindBridge(rootPath);
    // 注册事件监听
    this.registerMessageHandlers();

    /// 1. 在初始化时候开始构建代码索引
    // Index on initialization
    void this.ide.getWorkspaceDirs().then(async (dirs) => {
        // 获取扩展目录路径数组
        const extensions = await this.ide.getCodebaseExtensions();
        void this.refreshCodebaseIndex(dirs, extensions);
    });
  }

  invoke<T extends keyof ToCoreProtocol>(
    messageType: T,
    data: ToCoreProtocol[T][0],
  ): ToCoreProtocol[T][1] {
    return this.messenger.invoke(messageType, data);
  }

  send<T extends keyof FromCoreProtocol>(
    messageType: T,
    data: FromCoreProtocol[T][0],
    messageId?: string,
  ): string {
    return this.messenger.send(messageType, data, messageId);
  }

  private registerMessageHandlers() {
    // 只监听 files/created 事件
    /// TODO: 2. 在文件变更时触发增量索引 : 包含3个文件操作: files/changed files/created files
    this.messenger.on("files/created", async (message: Message<{ uris?: string[] }>) => {
      const { data, messageId } = message;
      if (data?.uris?.length) {
        console.error('Service Files created:', data.uris);
        // await refreshIfNotIgnored(data.uris);
        
        // 发送确认消息回去
        this.messenger.send("files/created/response", {
          success: true, 
          message: `已成功处理 ${data.uris.length} 个文件`,
          processedFiles: data.uris
        }, messageId);
      }
      // 不返回任何内容，以符合void返回类型要求
    });

    /// TODO: 3. 特殊文件变更时, 更新索引 : 如 gitignore 

    // TODO: 4. 手动触发 codebase 时

    // TOOD: 5 在分支变化时, 更新索引 (此处代码放在 插件那层), 直接触发 forceIndex
    /*

    这段代码的工作原理是：
    获取所有工作区目录
    对每个目录获取 Git 仓库
    监听仓库状态变化（repo.state.onDidChange）
    当状态变化时，检查当前分支是否与记录的上一个分支不同
    如果分支发生了变化，则调用 this.core.invoke("index/forceReIndex", { dirs: [dir] }) 触发对该目录的重新索引
    代码中还有一个有趣的注释，指出 onDidChange 回调的参数总是 undefined，所以它通过在 PREVIOUS_BRANCH_FOR_WORKSPACE_DIR 对象中记录上一个分支名称来检测变化。
    这解释了当 Git 分支变化时，Continue 如何重新索引代码库的机制。

    // Refresh index when branch is changed
    this.ide.getWorkspaceDirs().then((dirs) =>
        dirs.forEach(async (dir) => {
          const repo = await this.ide.getRepo(dir);
          if (repo) {
            repo.state.onDidChange(() => {
              // args passed to this callback are always undefined, so keep track of previous branch
              const currentBranch = repo?.state?.HEAD?.name;
              if (currentBranch) {
                if (this.PREVIOUS_BRANCH_FOR_WORKSPACE_DIR[dir]) {
                  if (
                    currentBranch !== this.PREVIOUS_BRANCH_FOR_WORKSPACE_DIR[dir]
                  ) {
                    // Trigger refresh of index only in this directory
                    this.core.invoke("index/forceReIndex", { dirs: [dir] });
                  }
                }
  
                this.PREVIOUS_BRANCH_FOR_WORKSPACE_DIR[dir] = currentBranch;
              }
            });
          }
        }),
      );

      */

    // 其他事件处理逻辑已注释
    /*
    on("files/deleted", async ({ data }) => {
      // ...
    });

    on("files/closed", async ({ data }) => {
      // ...
    });

    // ... 其他事件处理 ...
    */
  }

  async refreshCodebaseIndex(dirs: string[], extensions?: string[]) {
    if (dirs.length === 0) {
        console.error("没有找到工作区目录");
        return;
    }
    // TODO: 默认是单仓模式. 直接取第一个目录; 这里后续得看看 传入的是哪些目录(跟根目录还是子目录)
    const repoPath = dirs[0];
    console.error("repoPath", repoPath);

    if (!repoPath) {
        console.error("没有找到仓库路径");
        return;
    }

    const response = await this.bridge.prepare_search_index(repoPath, extensions, false);
    console.error("构建索引成功:", response);
  }
}
